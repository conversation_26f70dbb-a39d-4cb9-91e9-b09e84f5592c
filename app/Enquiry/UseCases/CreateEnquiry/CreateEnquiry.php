<?php

declare(strict_types=1);

namespace App\Enquiry\UseCases\CreateEnquiry;

use Shared\Mediator\Request;

/**
 * @implements Request<void>
 * @see CreateEnquiryHandler
 */
final class CreateEnquiry implements Request
{
    public function __construct(
        public readonly int $vendorId,
        public readonly string $source,
        public readonly Contact $contact,
        public readonly int $createdBy,
        public readonly array $metadata,
    ) {
    }
}
