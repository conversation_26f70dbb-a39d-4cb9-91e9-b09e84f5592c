<?php

declare(strict_types=1);

namespace App\Enquiry\UseCases\CreateEnquiry;

use App\User;
use Carbon\Carbon;
use App\AgentDepartment;
use Illuminate\Support\Arr;
use App\Events\LeadAssigned;
use Shared\Mediator\Request;
use App\BackendModel\Enquiry;
use App\BackendModel\LeadType;
use App\BackendModel\Settings;
use App\Events\CreateFollowup;
use App\BackendModel\EnquiryType;
use Shared\Mediator\RequestHandler;
use App\BackendModel\EnquiryPurpose;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\EnquiryFollowup;
use App\Enquiry\Events\EnquiryCreated;
use App\FrontendModel\LeadAdditionalDetails;
use App\Enquiry\Exceptions\EnquiryAlreadyExists;
use App\Modules\Facebook\Jobs\RecordEnquiry\ResolveEnquiryType;
use App\Modules\Facebook\Jobs\RecordEnquiry\RecordAdditionalAttributes;

final class CreateEnquiryHandler implements RequestHandler
{
    public function __construct(
        private readonly ResolveEnquiryType $resolveEnquiryType,
        private readonly RecordAdditionalAttributes $recordAdditionalAttributes
    ) {
    }

    /**
     * @param CreateEnquiry $request
     * @throws EnquiryAlreadyExists
     */
    public function handle(Request $request): void
    {
        $this->ensureEnquiryDoesNotExist(vendorId: $request->vendorId, contact: $request->contact);

        $enquiryType = $this->resolveEnquiryType
            ->with(source: $request->source, vendorId: $request->vendorId);

        $enquiry = $this->process(
            enquiryType: $enquiryType,
            contact: $request->contact,
            vendorId: $request->vendorId,
            cr
            metadata: $request->metadata
        );

        $this->recordAdditionalAttributes->for($enquiry, $request->vendorId, $request->metadata);

        if (Arr::has($request->metadata, 'department') && filled($request->metadata['department'])) {
            $this->assignStaffDepartmentWise(
                enquiry: $enquiry,
                vendorId: $request->vendorId,
                departmentName: $request->metadata['department']
            );

            $this->assignStoreDepartmentToField(
                enquiryId: $enquiry->pk_int_enquiry_id,
                vendorId: $request->vendorId,
                departmentName: $request->metadata['department']
            );
        }

        $this->attachNotesToEnquiry(
            enquiry: $enquiry,
            notes: $request->metadata['notes'] ?? '',
            metadata: $request->metadata
        );

        event(new EnquiryCreated(
            enquiryId: $enquiry->pk_int_enquiry_id,
            vendorId: $request->vendorId,
            enquiryTypeId: $enquiryType->pk_int_enquiry_type_id,
            source: $request->source
        ));
    }

    private function ensureEnquiryDoesNotExist(int $vendorId, Contact $contact): void
    {
        Enquiry::query()
            ->where('fk_int_user_id', '=', $vendorId)
            ->where('vchr_customer_mobile', '=', $contact->phoneNumber->toPhoneNumber())
            ->useWritePdo()
            ->doesntExistOr(fn () => throw EnquiryAlreadyExists::withPhoneNumber(
                phoneNumber: $contact->phoneNumber->toPhoneNumber(),
                vendorId: $vendorId
            ));
    }

    private function process(EnquiryType $enquiryType, Contact $contact, int $vendorId, int $createdBy, array $metadata): Enquiry
    {
        $attributes = $this->processAdditionalAttributes(vendorId: $vendorId, metadata: $metadata);

        $enquiry = new Enquiry();
        $enquiry->vchr_customer_mobile = $contact->phoneNumber->toPhoneNumber();
        $enquiry->mobile_no = $contact->phoneNumber->nationalNumber;
        $enquiry->country_code = $contact->phoneNumber->countryCode;
        $enquiry->read_status = 0;
        $enquiry->staff_id = $attributes['staff_id'] ?? null;
        $enquiry->fk_int_user_id = $vendorId;
        $enquiry->fk_int_enquiry_type_id = $enquiryType->pk_int_enquiry_type_id;
        $enquiry->feedback_status = $attributes['feedback_status_id'] ?? null;
        $enquiry->fk_int_purpose_id = $attributes['purpose_id'] ?? null;
        $enquiry->lead_type_id = $attributes['lead_type_id'] ?? null;
        $enquiry->vchr_customer_name = $contact->name;
        $enquiry->vchr_customer_email = $contact->email;
        $enquiry->vchr_enquiry_feedback = $metadata['feedback'] ?? null;
        $enquiry->more_phone_numbers = $metadata['more_phone_numbers'] ?? null;
        $enquiry->address = $metadata['address'] ?? null;
        $enquiry->created_by = $createdBy;
        $enquiry->vchr_customer_company_name = $metadata['company_name'] ?? null;
        $enquiry->save();

        return $enquiry;
    }

    private function processAdditionalAttributes(int $vendorId, array $metadata): array
    {
        if ($metadata === []) {
            return [];
        }

        $attributes = [];

        $attributes['feedback_status_id'] = $this->resolveFeedbackStatusId(
            status: Arr::has($metadata, 'status') && filled($metadata['status']) 
                ? $metadata['status'] 
                : 'New', 
            vendorId: $vendorId
        );

        if (Arr::has($metadata, 'purpose') && filled($metadata['purpose'])) {
            $attributes['purpose_id'] = $this->resolveEnquiryPurpose(
                purpose: $metadata['purpose'],
                vendorId: $vendorId
            );
        }

        if (Arr::has($metadata, 'type') && filled($metadata['type'])) {
            $attributes['lead_type_id'] = $this->resolveLeadType(type: $metadata['type'], vendorId: $vendorId);
        }

        if (Arr::has($metadata, 'staff_name') && filled($metadata['staff_name'])) {
            $staff = User::query()
                ->where('vchr_user_name', $metadata['staff_name'])
                ->where('parent_user_id', $vendorId)
                ->first(['pk_int_user_id']);

            $attributes['staff_id'] = $staff?->pk_int_user_id;
        }

        if (Arr::has($metadata, 'staff_id') && filled($metadata['staff_id'])) {
            $attributes['staff_id'] = $metadata['staff_id'];
        }

        return $attributes;
    }

    private function resolveFeedbackStatusId(string $status, int $vendorId): int
    {
        $feedbackStatus = FeedbackStatus::firstOrCreate(
            [
                'vchr_status' => $status,
                'fk_int_user_id' => $vendorId,
            ],
            [
                'vchr_color' => '#000000',
                'created_by' => $vendorId,
            ]
        );

        return $feedbackStatus->pk_int_feedback_status_id;
    }

    private function resolveEnquiryPurpose(string $purpose, int $vendorId): int
    {
        $feedbackPurpose = EnquiryPurpose::firstOrCreate(
            [
                'vchr_purpose' => $purpose,
                'fk_int_user_id' => $vendorId,
            ],
            [
                'vchr_purpose_description' => $purpose,
                'created_by' => $vendorId,
            ]
        );

        return $feedbackPurpose->pk_int_purpose_id;
    }

    private function resolveLeadType(string $type, int $vendorId): int
    {
        $leadType = LeadType::firstOrCreate(
            [
                'name' => $type,
                'vendor_id' => $vendorId,
            ],
            [
                'created_by' => $vendorId,
            ]
        );

        return $leadType->id;
    }

    private function assignStaffDepartmentWise(Enquiry $enquiry, int $vendorId, string $departmentName): void
    {
        $staff = AgentDepartment::query()
            ->join('departments', 'departments.id', '=', 'agent_departments.department_id')
            ->where('departments.vendor_id', $vendorId)
            ->where('departments.name', 'LIKE', '%' . $departmentName . '%')
            ->orderBy('agent_departments.count')
            ->select('agent_departments.*')
            ->first(['agent_departments.agent_id']);

        if (! $staff instanceof AgentDepartment) {
            return;
        }

        $enquiry->update([
            'staff_id' => $staff->agent_id,
            'assigned_date' => Carbon::now(),
        ]);
        event(new LeadAssigned($enquiry->pk_int_enquiry_id, $enquiry->created_by));
    }

    private function assignStoreDepartmentToField(int $enquiryId, int $vendorId, string $departmentName): void
    {
        $department = str_replace(['MARKETING', 'DISTRICTMANAGER'], '', $departmentName);

        if (! trim($department)) {
            return;
        }

        LeadAdditionalDetails::insert([
            'enquiry_id' => $enquiryId,
            'field_id' => 750,
            'field_name' => 'DISTRICT',
            'value' => trim($department), // Trim spaces to avoid empty values
            'created_by' => $vendorId,
        ]);
    }

    private function attachNotesToEnquiry(Enquiry $enquiry, string $notes, array $metadata): void
    {
        $notes = $this->appendEnquiryNotesToFollowup(
            enquiry: $enquiry,
            followupNote: $notes,
            enquiryNote: $metadata['notes'] ?? ''
        );

        if ($notes === '') {
            return;
        }

        $this->createFollowUp(
            lead: $enquiry,
            note: $notes,
            vendorId: $enquiry->fk_int_user_id,
            enquiryFollowUpType: EnquiryFollowup::TYPE_NOTE
        );
    }

    private function appendEnquiryNotesToFollowup(Enquiry $enquiry, string $followupNote, string $enquiryNote): string
    {
        $setting = Settings::query()
            ->where('fk_int_user_id', $enquiry->fk_int_user_id)
            ->where('vchr_settings_type', 'append-enquiry-attributes-as-notes')
            ->first('vchr_settings_value');

        if (! $setting && $setting === 'false') {
            return $followupNote;
        }

        return $followupNote . "\n\n" . $enquiryNote;
    }

    private function createFollowUp(Enquiry $lead, string $note, int $vendorId, int $enquiryFollowUpType): void
    {
        event(new CreateFollowup($note, $enquiryFollowUpType, $lead->pk_int_enquiry_id, $vendorId));
    }
}
